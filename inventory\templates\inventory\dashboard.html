{% extends "inventory/base_inventory.html" %}
{% load static %}
{% load i18n %}
{% load purchase_permission_tags %}

{% block title %}{% trans "لوحة تحكم المخزن" %}{% endblock %}

{% block extra_css %}
<style>
    /* ===== DASHBOARD HEADER STYLES ===== */
    .dashboard-container {
        padding: 0;
        background: linear-gradient(135deg, #f5f7fa 0%, #c3cfe2 100%);
        min-height: calc(100vh - 100px);
    }

    .dashboard-header {
        background: linear-gradient(135deg, var(--primary-color) 0%, #303f9f 100%);
        color: white;
        padding: 2rem 0;
        margin-bottom: 2rem;
        position: relative;
        overflow: hidden;
    }

    .dashboard-header::before {
        content: '';
        position: absolute;
        top: 0;
        left: 0;
        right: 0;
        bottom: 0;
        background: url('data:image/svg+xml,<svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 100 100"><defs><pattern id="grain" width="100" height="100" patternUnits="userSpaceOnUse"><circle cx="25" cy="25" r="1" fill="rgba(255,255,255,0.1)"/><circle cx="75" cy="75" r="1" fill="rgba(255,255,255,0.1)"/><circle cx="50" cy="10" r="0.5" fill="rgba(255,255,255,0.05)"/></pattern></defs><rect width="100" height="100" fill="url(%23grain)"/></svg>');
        opacity: 0.3;
    }

    .dashboard-header-content {
        position: relative;
        z-index: 2;
        max-width: 1200px;
        margin: 0 auto;
        padding: 0 2rem;
    }

    .dashboard-title {
        font-size: 2.5rem;
        font-weight: 700;
        margin: 0 0 0.5rem 0;
        display: flex;
        align-items: center;
        gap: 1rem;
        text-shadow: 0 2px 4px rgba(0,0,0,0.1);
    }

    .dashboard-title-icon {
        font-size: 2.2rem;
        opacity: 0.9;
    }

    .dashboard-subtitle {
        font-size: 1.1rem;
        opacity: 0.9;
        margin: 0;
        font-weight: 400;
        letter-spacing: 0.5px;
    }

    /* ===== STATISTICS CARDS STYLES ===== */
    .stats-grid {
        display: grid;
        grid-template-columns: repeat(auto-fit, minmax(280px, 1fr));
        gap: 1.5rem;
        margin-bottom: 2rem;
        padding: 0 2rem;
    }

    .stats-card {
        background: white;
        border-radius: 16px;
        padding: 1.5rem;
        box-shadow: 0 4px 20px rgba(0, 0, 0, 0.08);
        border: 1px solid rgba(0, 0, 0, 0.05);
        transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
        position: relative;
        overflow: hidden;
        height: 100%;
        display: flex;
        flex-direction: column;
    }

    .stats-card::before {
        content: '';
        position: absolute;
        top: 0;
        left: 0;
        right: 0;
        height: 4px;
        background: linear-gradient(90deg, var(--card-accent-color, #3f51b5), var(--card-accent-secondary, #5c6bc0));
        transition: height 0.3s ease;
    }

    .stats-card:hover {
        transform: translateY(-8px);
        box-shadow: 0 12px 40px rgba(0, 0, 0, 0.15);
    }

    .stats-card:hover::before {
        height: 6px;
    }

    .stats-card-header {
        display: flex;
        align-items: center;
        justify-content: space-between;
        margin-bottom: 1rem;
    }

    .stats-card-icon {
        width: 60px;
        height: 60px;
        border-radius: 12px;
        display: flex;
        align-items: center;
        justify-content: center;
        font-size: 1.5rem;
        color: white;
        background: linear-gradient(135deg, var(--card-accent-color, #3f51b5), var(--card-accent-secondary, #5c6bc0));
        box-shadow: 0 4px 12px rgba(0, 0, 0, 0.15);
    }

    .stats-card-content {
        flex: 1;
        margin-bottom: 1rem;
    }

    .stats-card-title {
        font-size: 0.9rem;
        color: #6c757d;
        margin-bottom: 0.5rem;
        font-weight: 500;
        letter-spacing: 0.3px;
    }

    .stats-card-value {
        font-size: 2.2rem;
        font-weight: 700;
        color: #2c3e50;
        margin: 0;
        line-height: 1;
    }

    .stats-card-footer {
        margin-top: auto;
        padding-top: 1rem;
        border-top: 1px solid rgba(0, 0, 0, 0.05);
    }

    .stats-card-link {
        display: flex;
        align-items: center;
        justify-content: space-between;
        color: var(--card-accent-color, #3f51b5);
        text-decoration: none;
        font-weight: 500;
        font-size: 0.9rem;
        transition: all 0.2s ease;
        padding: 0.5rem 0;
    }

    .stats-card-link:hover {
        color: var(--card-accent-secondary, #5c6bc0);
        transform: translateX(-2px);
    }

    .stats-card-link i {
        transition: transform 0.2s ease;
    }

    .stats-card-link:hover i {
        transform: translateX(-3px);
    }

    /* Card Color Variants */
    .stats-card-primary {
        --card-accent-color: #3f51b5;
        --card-accent-secondary: #5c6bc0;
    }

    .stats-card-warning {
        --card-accent-color: #ff9800;
        --card-accent-secondary: #ffb74d;
    }

    .stats-card-danger {
        --card-accent-color: #f44336;
        --card-accent-secondary: #ef5350;
    }

    .stats-card-success {
        --card-accent-color: #4caf50;
        --card-accent-secondary: #66bb6a;
    }

    /* Content Section */
    .content-section {
        padding: 0 2rem 2rem;
    }

    /* ===== RESPONSIVE DESIGN ===== */
    @media (max-width: 1200px) {
        .stats-grid {
            grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
            gap: 1.2rem;
        }

        .dashboard-title {
            font-size: 2.2rem;
        }
    }

    @media (max-width: 992px) {
        .stats-grid {
            grid-template-columns: repeat(auto-fit, minmax(220px, 1fr));
            gap: 1rem;
            padding: 0 1.5rem;
        }

        .dashboard-header-content {
            padding: 0 1.5rem;
        }

        .content-section {
            padding: 0 1.5rem 2rem;
        }

        .dashboard-title {
            font-size: 2rem;
        }

        .stats-card {
            padding: 1.2rem;
        }
    }

    @media (max-width: 768px) {
        .stats-grid {
            grid-template-columns: 1fr;
            gap: 1rem;
            padding: 0 1rem;
        }

        .dashboard-header {
            padding: 1.5rem 0;
            margin-bottom: 1.5rem;
        }

        .dashboard-header-content {
            padding: 0 1rem;
        }

        .content-section {
            padding: 0 1rem 1.5rem;
        }

        .dashboard-title {
            font-size: 1.8rem;
            flex-direction: column;
            gap: 0.5rem;
            text-align: center;
        }

        .dashboard-title-icon {
            font-size: 2rem;
        }

        .dashboard-subtitle {
            text-align: center;
            font-size: 1rem;
        }

        .stats-card {
            padding: 1rem;
        }

        .stats-card-icon {
            width: 50px;
            height: 50px;
            font-size: 1.3rem;
        }

        .stats-card-value {
            font-size: 1.8rem;
        }
    }

    @media (max-width: 480px) {
        .dashboard-title {
            font-size: 1.5rem;
        }

        .dashboard-subtitle {
            font-size: 0.9rem;
        }

        .stats-card-value {
            font-size: 1.6rem;
        }

        .stats-card-title {
            font-size: 0.85rem;
        }
    }

    /* ===== RTL SUPPORT ===== */
    [dir="rtl"] .stats-card-link {
        flex-direction: row-reverse;
    }

    [dir="rtl"] .stats-card-link:hover {
        transform: translateX(2px);
    }

    [dir="rtl"] .stats-card-link:hover i {
        transform: translateX(3px);
    }

    [dir="rtl"] .dashboard-title {
        text-align: right;
    }

    [dir="rtl"] .dashboard-subtitle {
        text-align: right;
    }

    @media (max-width: 768px) {
        [dir="rtl"] .dashboard-title,
        [dir="rtl"] .dashboard-subtitle {
            text-align: center;
        }
    }

    /* ===== ACCESSIBILITY IMPROVEMENTS ===== */
    .stats-card:focus-within {
        outline: 2px solid var(--card-accent-color);
        outline-offset: 2px;
    }

    .stats-card-link:focus {
        outline: 2px solid var(--card-accent-color);
        outline-offset: 2px;
        border-radius: 4px;
    }

    /* Reduced motion support */
    @media (prefers-reduced-motion: reduce) {
        .stats-card,
        .stats-card-link,
        .stats-card-link i,
        .stats-card::before {
            transition: none;
        }

        .stats-card:hover {
            transform: none;
        }

        .stats-card-link:hover {
            transform: none;
        }

        .stats-card-link:hover i {
            transform: none;
        }
    }

    /* High contrast mode support */
    @media (prefers-contrast: high) {
        .stats-card {
            border: 2px solid #000;
        }

        .stats-card-title {
            color: #000;
        }

        .stats-card-value {
            color: #000;
        }
    }

    /* Dark mode support (if implemented) */
    @media (prefers-color-scheme: dark) {
        .dashboard-container {
            background: linear-gradient(135deg, #1a1a1a 0%, #2d3748 100%);
        }

        .stats-card {
            background: #2d3748;
            border-color: #4a5568;
            color: #e2e8f0;
        }

        .stats-card-title {
            color: #a0aec0;
        }

        .stats-card-value {
            color: #e2e8f0;
        }
    }
</style>
{% endblock %}

{% block content %}
{% csrf_token %}

<div class="dashboard-container">
    <!-- Enhanced Dashboard Header -->
    <div class="dashboard-header">
        <div class="dashboard-header-content">
            <h1 class="dashboard-title">
                <i class="fas fa-warehouse dashboard-title-icon"></i>
                <span>{% trans "لوحة تحكم المخزن" %}</span>
            </h1>
            <p class="dashboard-subtitle">نظرة عامة شاملة على حالة المخزن والمنتجات</p>
        </div>
    </div>

    <!-- Enhanced Statistics Cards -->
    <div class="stats-grid">
        <div class="stats-card stats-card-primary">
            <div class="stats-card-header">
                <div class="stats-card-icon">
                    <i class="fas fa-boxes"></i>
                </div>
            </div>
            <div class="stats-card-content">
                <div class="stats-card-title">{% trans "إجمالي الأصناف" %}</div>
                <div class="stats-card-value">{{ total_products }}</div>
            </div>
            <div class="stats-card-footer">
                <a href="{% url 'inventory:product_list' %}" class="stats-card-link">
                    <span>{% trans "عرض التفاصيل" %}</span>
                    <i class="fas fa-arrow-left"></i>
                </a>
            </div>
        </div>

        <div class="stats-card stats-card-warning">
            <div class="stats-card-header">
                <div class="stats-card-icon">
                    <i class="fas fa-exclamation-triangle"></i>
                </div>
            </div>
            <div class="stats-card-content">
                <div class="stats-card-title">{% trans "أصناف منخفضة المخزون" %}</div>
                <div class="stats-card-value">{{ low_stock_count }}</div>
            </div>
            <div class="stats-card-footer">
                <a href="{% url 'inventory:product_list' %}?stock_status=low" class="stats-card-link">
                    <span>{% trans "عرض التفاصيل" %}</span>
                    <i class="fas fa-arrow-left"></i>
                </a>
            </div>
        </div>

        <div class="stats-card stats-card-danger">
            <div class="stats-card-header">
                <div class="stats-card-icon">
                    <i class="fas fa-times-circle"></i>
                </div>
            </div>
            <div class="stats-card-content">
                <div class="stats-card-title">{% trans "أصناف غير متوفرة" %}</div>
                <div class="stats-card-value">{{ out_of_stock_count }}</div>
            </div>
            <div class="stats-card-footer">
                <a href="{% url 'inventory:product_list' %}?stock_status=out" class="stats-card-link">
                    <span>{% trans "عرض التفاصيل" %}</span>
                    <i class="fas fa-arrow-left"></i>
                </a>
            </div>
        </div>

        <div class="stats-card stats-card-success">
            <div class="stats-card-header">
                <div class="stats-card-icon">
                    <i class="fas fa-file-invoice"></i>
                </div>
            </div>
            <div class="stats-card-content">
                <div class="stats-card-title">{% trans "إجمالي الأذونات" %}</div>
                <div class="stats-card-value">{{ total_vouchers }}</div>
            </div>
            <div class="stats-card-footer">
                <a href="{% url 'inventory:voucher_list' %}" class="stats-card-link">
                    <span>{% trans "عرض التفاصيل" %}</span>
                    <i class="fas fa-arrow-left"></i>
                </a>
            </div>
        </div>
    </div>

    <div class="content-section">
        <!-- Content Grid -->
        <div class="row g-4">
            <!-- أصناف تحتاج طلب شراء -->
            <div class="col-xl-6">
                <div class="card h-100">
                    <div class="card-header d-flex align-items-center">
                        <i class="fas fa-exclamation-triangle text-warning me-2"></i>
                        <h5 class="mb-0">{% trans "أصناف تحتاج لطلب شراء" %}</h5>
                    </div>
                    <div class="card-body">
                        {% if purchase_needed_products %}
                        <div class="table-responsive">
                            <table class="table table-hover">
                                <thead class="table-light">
                                    <tr>
                                        <th>{% trans "كود الصنف" %}</th>
                                        <th>{% trans "اسم الصنف" %}</th>
                                        <th>{% trans "الكمية الحالية" %}</th>
                                        <th>{% trans "الحد الأدنى" %}</th>
                                        <th>{% trans "إجراءات" %}</th>
                                    </tr>
                                </thead>
                                <tbody>
                                    {% for product in purchase_needed_products %}
                                    <tr>
                                        <td><span class="badge bg-secondary">{{ product.product_id }}</span></td>
                                        <td class="fw-medium">{{ product.name }}</td>
                                        <td><span class="badge bg-danger">{{ product.quantity }}</span></td>
                                        <td><span class="badge bg-warning">{{ product.minimum_threshold }}</span></td>
                                        <td>
                                            {% has_purchase_module_permission "purchase_items" "add" as can_create_purchase %}
                                            {% if can_create_purchase %}
                                                <button data-product-id="{{ product.product_id }}" class="btn btn-primary btn-sm purchase-request-btn">
                                                    <i class="fas fa-shopping-cart"></i> {% trans "إنشاء طلب شراء" %}
                                                </button>
                                            {% else %}
                                                <button class="btn btn-secondary btn-sm" disabled title="{% trans 'لا تملك صلاحية إنشاء طلب شراء' %}">
                                                    <i class="fas fa-shopping-cart"></i> {% trans "إنشاء طلب شراء" %}
                                                </button>
                                            {% endif %}
                                        </td>
                                    </tr>
                                    {% endfor %}
                                </tbody>
                            </table>
                        </div>
                        {% else %}
                        <div class="alert alert-success d-flex align-items-center">
                            <i class="fas fa-check-circle me-2"></i>
                            {% trans "لا توجد أصناف تحتاج لطلب شراء حالياً." %}
                        </div>
                        {% endif %}
                    </div>
                </div>
            </div>

            <!-- آخر الاذونات -->
            <div class="col-xl-6">
                <div class="card h-100">
                    <div class="card-header d-flex align-items-center">
                        <i class="fas fa-clipboard-list text-info me-2"></i>
                        <h5 class="mb-0">{% trans "آخر الأذونات" %}</h5>
                    </div>
                    <div class="card-body">
                        {% if recent_vouchers %}
                        <div class="table-responsive">
                            <table class="table table-hover">
                                <thead class="table-light">
                                    <tr>
                                        <th>{% trans "رقم الإذن" %}</th>
                                        <th>{% trans "نوع الإذن" %}</th>
                                        <th>{% trans "التاريخ" %}</th>
                                        <th>{% trans "إجراءات" %}</th>
                                    </tr>
                                </thead>
                                <tbody>
                                    {% for voucher in recent_vouchers %}
                                    <tr>
                                        <td><span class="badge bg-primary">{{ voucher.voucher_number }}</span></td>
                                        <td class="fw-medium">{{ voucher.get_voucher_type_display }}</td>
                                        <td class="text-muted">{{ voucher.date }}</td>
                                        <td>
                                            <a href="{% url 'inventory:voucher_detail' voucher.voucher_number %}" class="btn btn-outline-info btn-sm">
                                                <i class="fas fa-eye"></i> {% trans "عرض" %}
                                            </a>
                                        </td>
                                    </tr>
                                    {% endfor %}
                                </tbody>
                            </table>
                        </div>
                        {% else %}
                        <div class="alert alert-info d-flex align-items-center">
                            <i class="fas fa-info-circle me-2"></i>
                            {% trans "لا توجد أذونات حتى الآن." %}
                        </div>
                        {% endif %}
                    </div>
                </div>
            </div>
        </div>

        <!-- الأصناف منخفضة المخزون -->
        <div class="row mt-4">
            <div class="col-12">
                <div class="card">
                    <div class="card-header d-flex align-items-center justify-content-between">
                        <div class="d-flex align-items-center">
                            <i class="fas fa-bell text-warning me-2"></i>
                            <h5 class="mb-0">{% trans "أصناف منخفضة المخزون" %}</h5>
                        </div>
                        {% if low_stock_products %}
                        <span class="badge bg-warning">{{ low_stock_products|length }}</span>
                        {% endif %}
                    </div>
                    <div class="card-body">
                        {% if low_stock_products %}
                        <div class="table-responsive">
                            <table class="table table-hover">
                                <thead class="table-light">
                                    <tr>
                                        <th>{% trans "كود الصنف" %}</th>
                                        <th>{% trans "اسم الصنف" %}</th>
                                        <th>{% trans "الكمية الحالية" %}</th>
                                        <th>{% trans "الحد الأدنى" %}</th>
                                        <th>{% trans "الحد الأقصى" %}</th>
                                        <th>{% trans "الوحدة" %}</th>
                                        <th>{% trans "إجراءات" %}</th>
                                    </tr>
                                </thead>
                                <tbody>
                                    {% for product in low_stock_products %}
                                    <tr>
                                        <td><span class="badge bg-secondary">{{ product.product_id }}</span></td>
                                        <td class="fw-medium">{{ product.name }}</td>
                                        <td><span class="badge bg-danger">{{ product.quantity }}</span></td>
                                        <td><span class="badge bg-warning">{{ product.minimum_threshold }}</span></td>
                                        <td><span class="badge bg-success">{{ product.maximum_threshold }}</span></td>
                                        <td class="text-muted">{{ product.unit }}</td>
                                        <td>
                                            <div class="btn-group" role="group">
                                                <a href="{% url 'inventory:product_edit' product.product_id %}" class="btn btn-outline-warning btn-sm">
                                                    <i class="fas fa-edit"></i> {% trans "تعديل" %}
                                                </a>
                                                {% has_purchase_module_permission "purchase_items" "add" as can_create_purchase %}
                                                {% if can_create_purchase %}
                                                    <button data-product-id="{{ product.product_id }}" class="btn btn-outline-primary btn-sm purchase-request-btn">
                                                        <i class="fas fa-shopping-cart"></i> {% trans "طلب شراء" %}
                                                    </button>
                                                {% else %}
                                                    <button class="btn btn-outline-secondary btn-sm" disabled title="{% trans 'لا تملك صلاحية إنشاء طلب شراء' %}">
                                                        <i class="fas fa-shopping-cart"></i> {% trans "طلب شراء" %}
                                                    </button>
                                                {% endif %}
                                            </div>
                                        </td>
                                    </tr>
                                    {% endfor %}
                                </tbody>
                            </table>
                        </div>
                        <div class="mt-3 d-flex justify-content-center">
                            <a href="{% url 'inventory:product_list' %}?stock_status=low" class="btn btn-primary">
                                <i class="fas fa-list me-2"></i> {% trans "عرض كل الأصناف منخفضة المخزون" %}
                            </a>
                        </div>
                        {% else %}
                        <div class="alert alert-success d-flex align-items-center">
                            <i class="fas fa-check-circle me-2"></i>
                            {% trans "لا توجد أصناف منخفضة المخزون حالياً." %}
                        </div>
                        {% endif %}
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

<!-- Enhanced Purchase Request Modal -->
<div class="modal fade" id="purchaseRequestModal" tabindex="-1" aria-labelledby="purchaseRequestModalLabel" aria-hidden="true">
    <div class="modal-dialog modal-dialog-centered modal-lg">
        <div class="modal-content border-0 shadow-lg">
            <div class="modal-header bg-gradient text-white" style="background: linear-gradient(135deg, var(--primary-color) 0%, #303f9f 100%);">
                <h5 class="modal-title d-flex align-items-center" id="purchaseRequestModalLabel">
                    <i class="fas fa-shopping-cart me-2"></i>
                    {% trans "طلب شراء صنف" %}
                </h5>
                <button type="button" class="btn-close btn-close-white" data-bs-dismiss="modal" aria-label="Close"></button>
            </div>
            <div class="modal-body p-4">
                <div class="product-info mb-4 p-3 rounded-3" style="background: linear-gradient(135deg, #f8f9fa 0%, #e9ecef 100%); border-left: 4px solid var(--primary-color);">
                    <h6 class="mb-3 d-flex align-items-center text-primary">
                        <i class="fas fa-info-circle me-2"></i>
                        {% trans "معلومات الصنف" %}
                    </h6>
                    <div class="row g-3">
                        <div class="col-md-6">
                            <div class="d-flex align-items-center">
                                <strong class="text-muted me-2">{% trans "رقم الصنف:" %}</strong>
                                <span class="badge bg-secondary" id="modal-product-id"></span>
                            </div>
                        </div>
                        <div class="col-md-6">
                            <div class="d-flex align-items-center">
                                <strong class="text-muted me-2">{% trans "اسم الصنف:" %}</strong>
                                <span class="fw-medium" id="modal-product-name"></span>
                            </div>
                        </div>
                        <div class="col-md-6">
                            <div class="d-flex align-items-center">
                                <strong class="text-muted me-2">{% trans "الكمية الحالية:" %}</strong>
                                <span class="badge bg-danger" id="modal-product-quantity"></span>
                            </div>
                        </div>
                        <div class="col-md-6">
                            <div class="d-flex align-items-center">
                                <strong class="text-muted me-2">{% trans "الحد الأدنى:" %}</strong>
                                <span class="badge bg-warning" id="modal-product-min"></span>
                            </div>
                        </div>
                    </div>
                </div>

                <form id="purchaseRequestForm">
                    <div class="mb-3">
                        <label for="quantityRequested" class="form-label fw-medium">
                            <i class="fas fa-calculator me-1 text-primary"></i>
                            {% trans "الكمية المطلوبة" %}
                        </label>
                        <input type="number" class="form-control form-control-lg" id="quantityRequested" min="1" required
                               placeholder="{% trans 'أدخل الكمية المطلوبة' %}">
                        <div class="form-text">
                            <i class="fas fa-lightbulb me-1"></i>
                            {% trans "حدد الكمية المطلوبة للشراء." %}
                        </div>
                    </div>
                    <div class="mb-3">
                        <label for="requestNotes" class="form-label fw-medium">
                            <i class="fas fa-sticky-note me-1 text-primary"></i>
                            {% trans "ملاحظات (اختياري)" %}
                        </label>
                        <textarea class="form-control" id="requestNotes" rows="3"
                                  placeholder="{% trans 'أضف أي ملاحظات إضافية...' %}"></textarea>
                    </div>
                    <div id="purchaseRequestStatus" class="alert d-none">
                        <!-- سيتم استخدام هذا العنصر لعرض حالة الطلب إذا كان موجودًا بالفعل -->
                    </div>
                </form>
            </div>
            <div class="modal-footer bg-light border-0 p-3">
                <button type="button" class="btn btn-outline-secondary" data-bs-dismiss="modal">
                    <i class="fas fa-times me-1"></i>
                    {% trans "إلغاء" %}
                </button>
                <button type="button" class="btn btn-primary" id="submitPurchaseRequest">
                    <i class="fas fa-paper-plane me-1"></i>
                    {% trans "إرسال طلب الشراء" %}
                </button>
            </div>
        </div>
    </div>
</div>

{% endblock %}

{% block extra_js %}
<script src="https://code.jquery.com/jquery-3.7.1.min.js"></script>
<script>
document.addEventListener('DOMContentLoaded', function() {
    // كائن Bootstrap Modal
    const purchaseModal = new bootstrap.Modal(document.getElementById('purchaseRequestModal'));
    
    // العناصر الرئيسية
    const modalProductId = document.getElementById('modal-product-id');
    const modalProductName = document.getElementById('modal-product-name');
    const modalProductQuantity = document.getElementById('modal-product-quantity');
    const modalProductMin = document.getElementById('modal-product-min');
    const quantityInput = document.getElementById('quantityRequested');
    const notesInput = document.getElementById('requestNotes');
    const statusDiv = document.getElementById('purchaseRequestStatus');
    const submitButton = document.getElementById('submitPurchaseRequest');
    
    // متغير لتخزين معرف الصنف المحدد حاليا
    let currentProductId = null;
    let currentButton = null;
    
    // إضافة مستمع الحدث لأزرار طلب الشراء
    const purchaseButtons = document.querySelectorAll('.purchase-request-btn');
    purchaseButtons.forEach(button => {
        button.addEventListener('click', function() {
            const productId = this.getAttribute('data-product-id');
            currentProductId = productId;
            currentButton = this;
            
            // تجميع الصف الذي يحتوي على بيانات المنتج
            const row = this.closest('tr');
            
            if (row) {
                const productName = row.cells[1].textContent;
                const productQuantity = row.cells[2].textContent;
                const productMin = row.cells[3].textContent;
                
                // ملء بيانات المودال
                modalProductId.textContent = productId;
                modalProductName.textContent = productName;
                modalProductQuantity.textContent = productQuantity;
                modalProductMin.textContent = productMin;
                
                // حساب الكمية المقترحة (الفرق بين الحد الأدنى والكمية الحالية)
                const qtyDiff = parseFloat(productMin) - parseFloat(productQuantity);
                quantityInput.value = qtyDiff > 0 ? qtyDiff : 1;
                
                // التحقق إذا كان الصنف موجود بالفعل في طلب شراء
                checkProductInPurchaseRequest(productId);
            }
        });
    });
    
    // وظيفة للتحقق مما إذا كان المنتج موجودًا بالفعل في طلب شراء
    function checkProductInPurchaseRequest(productId) {
        fetch(`/purchase/api/check-product/${productId}/`)
            .then(response => response.json())
            .then(data => {
                if (data.in_purchase_request) {
                    // إظهار رسالة تحذير إذا كان المنتج موجودًا بالفعل
                    statusDiv.innerHTML = `<strong>تنبيه:</strong> هذا الصنف موجود بالفعل في طلب شراء بحالة "${data.request_status === 'pending' ? 'قيد الانتظار' : 
                                        data.request_status === 'approved' ? 'تمت الموافقة' : 
                                        data.request_status === 'rejected' ? 'مرفوض' : 
                                        data.request_status === 'completed' ? 'مكتمل' : data.request_status}".`;
                    statusDiv.classList.remove('d-none', 'alert-success', 'alert-warning', 'alert-danger');
                    
                    // تغيير لون التنبيه حسب الحالة
                    if (data.request_status === 'pending' || data.request_status === 'approved') {
                        statusDiv.classList.add('alert-warning');
                        // تعطيل زر الإرسال لأن هناك طلب قيد المعالجة
                        submitButton.disabled = true;
                        submitButton.title = 'لا يمكن إنشاء طلب جديد لهذا الصنف حتى يكتمل الطلب الحالي';
                    } else if (data.request_status === 'rejected') {
                        statusDiv.classList.add('alert-danger');
                        submitButton.disabled = false;
                    } else if (data.request_status === 'completed') {
                        statusDiv.classList.add('alert-success');
                        submitButton.disabled = false;
                    }
                } else {
                    // إذا لم يكن المنتج موجودًا في أي طلب، إخفاء حالة التحذير
                    statusDiv.classList.add('d-none');
                    submitButton.disabled = false;
                    submitButton.title = '';
                }
                
                // عرض المودال بعد الانتهاء من التحقق
                purchaseModal.show();
            })
            .catch(error => {
                console.error('Error checking product status:', error);
                // عرض المودال على أي حال
                statusDiv.classList.add('d-none');
                purchaseModal.show();
            });
    }
    
    // إضافة مستمع لزر إرسال طلب الشراء
    submitButton.addEventListener('click', function() {
        if (!currentProductId || !quantityInput.value || parseFloat(quantityInput.value) <= 0) {
            alert('يرجى إدخال كمية صالحة.');
            return;
        }
        
        // تعطيل الزر أثناء المعالجة
        this.disabled = true;
        const originalButtonText = this.textContent;
        this.innerHTML = '<i class="fas fa-spinner fa-spin"></i> جاري الإرسال...';
        
        // الحصول على CSRF token
        const csrfToken = getCsrfToken();
        
        // إرسال طلب الشراء مع الكمية المطلوبة
        fetch('{% url "Purchase_orders:transfer_product_to_purchase_request" %}', {
            method: 'POST',
            headers: {
                'Content-Type': 'application/json',
                'X-CSRFToken': csrfToken,
                'X-Requested-With': 'XMLHttpRequest'
            },
            body: JSON.stringify({
                'product_id': currentProductId,
                'action': 'add',
                'quantity_requested': parseFloat(quantityInput.value),
                'notes': notesInput.value
            })
        })
        .then(response => {
            if (response.ok) {
                return response.json();
            } else {
                throw new Error('Request failed with status ' + response.status);
            }
        })
        .then(data => {
            console.log('Success response:', data);
            
            // إغلاق المودال
            purchaseModal.hide();
            
            // تحديث زر الصنف في الجدول
            if (currentButton) {
                currentButton.className = 'btn btn-success btn-sm';
                currentButton.innerHTML = '<i class="fas fa-check"></i> تمت الإضافة';
                currentButton.disabled = true;
            }
            
            // إظهار رسالة نجاح
            alert('تم إضافة المنتج إلى طلب الشراء بنجاح');
        })
        .catch(error => {
            console.error('Error creating purchase request:', error);
            alert('حدث خطأ أثناء إنشاء طلب الشراء. الرجاء المحاولة مرة أخرى.');
            
            // إعادة تمكين الزر
            this.disabled = false;
            this.innerHTML = originalButtonText;
        });
    });
    
    // وظيفة مساعدة للحصول على CSRF token
    function getCsrfToken() {
        // البحث عن الـ token في عنصر input المخفي
        const csrfInput = document.querySelector('input[name="csrfmiddlewaretoken"]');
        if (csrfInput) {
            return csrfInput.value;
        }
        
        // البحث عن الـ token في الكوكيز كخيار ثانوي
        const cookieValue = document.cookie
            .split('; ')
            .find(row => row.startsWith('csrftoken='))
            ?.split('=')[1];
        
        return cookieValue || '';
    }
});
</script>
{% endblock %}
