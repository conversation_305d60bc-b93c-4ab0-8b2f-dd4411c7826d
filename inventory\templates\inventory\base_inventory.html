{% load static %}
{% load i18n %}
{% load inventory_permission_tags %}
<!DOCTYPE html>
<html lang="{{ current_language }}" dir="{{ text_direction }}">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>{% block title %}{% trans "نظام إدارة المخزن" %}{% endblock %}</title>

    <!-- Bootstrap RTL/LTR -->
    {% if text_direction == 'rtl' %}
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.2/dist/css/bootstrap.rtl.min.css" rel="stylesheet">
    {% else %}
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.2/dist/css/bootstrap.min.css" rel="stylesheet">
    {% endif %}

    <!-- Font Awesome -->
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.5.1/css/all.min.css">

    <!-- Google Fonts -->
    {% if system_settings.font_family == 'cairo' or current_font == 'Cairo' %}
    <link href="https://fonts.googleapis.com/css2?family=Cairo:wght@400;600;700&display=swap" rel="stylesheet">
    {% elif system_settings.font_family == 'tajawal' or current_font == 'Tajawal' %}
    <link href="https://fonts.googleapis.com/css2?family=Tajawal:wght@400;500;700&display=swap" rel="stylesheet">
    {% elif system_settings.font_family == 'almarai' or current_font == 'Almarai' %}
    <link href="https://fonts.googleapis.com/css2?family=Almarai:wght@400;700&display=swap" rel="stylesheet">
    {% elif system_settings.font_family == 'ibm-plex-sans-arabic' or current_font == 'IBM Plex Sans Arabic' %}
    <link href="https://fonts.googleapis.com/css2?family=IBM+Plex+Sans+Arabic:wght@400;500;600&display=swap" rel="stylesheet">
    {% elif system_settings.font_family == 'noto-sans-arabic' or current_font == 'Noto Sans Arabic' %}
    <link href="https://fonts.googleapis.com/css2?family=Noto+Sans+Arabic:wght@300;400;500;600;700;800&display=swap" rel="stylesheet">
    {% else %}
    <link href="https://fonts.googleapis.com/css2?family=Cairo:wght@400;600;700&display=swap" rel="stylesheet">
    {% endif %}

    <style>
        :root {
            --font-family: {% if system_settings.font_family %}
                {% if system_settings.font_family == 'cairo' %}'Cairo'
                {% elif system_settings.font_family == 'tajawal' %}'Tajawal'
                {% elif system_settings.font_family == 'almarai' %}'Almarai'
                {% elif system_settings.font_family == 'ibm-plex-sans-arabic' %}'IBM Plex Sans Arabic'
                {% elif system_settings.font_family == 'noto-sans-arabic' %}'Noto Sans Arabic'
                {% else %}'Cairo'
                {% endif %}
                {% else %}{{ current_font|default:'Cairo' }}{% endif %}, sans-serif;

            /* Enhanced Color System */
            --primary-color: #3f51b5;
            --primary-light: #5c6bc0;
            --primary-dark: #303f9f;
            --secondary-color: #ff4081;
            --secondary-light: #ff6ec7;
            --secondary-dark: #c51162;
            --success-color: #4caf50;
            --success-light: #66bb6a;
            --success-dark: #388e3c;
            --info-color: #00bcd4;
            --info-light: #26c6da;
            --info-dark: #0097a7;
            --warning-color: #ff9800;
            --warning-light: #ffb74d;
            --warning-dark: #f57c00;
            --danger-color: #f44336;
            --danger-light: #ef5350;
            --danger-dark: #d32f2f;
            --light-color: #f5f5f5;
            --dark-color: #212121;

            /* Enhanced Background System */
            --body-bg: linear-gradient(135deg, #f5f7fa 0%, #c3cfe2 100%);
            --card-bg: #ffffff;
            --header-bg: linear-gradient(135deg, var(--primary-color) 0%, var(--primary-dark) 100%);

            /* Enhanced Shadow System */
            --card-shadow: 0 4px 20px rgba(0, 0, 0, 0.08);
            --card-shadow-hover: 0 12px 40px rgba(0, 0, 0, 0.15);
            --card-shadow-lg: 0 8px 32px rgba(0, 0, 0, 0.12);

            /* Spacing System */
            --spacing-xs: 0.25rem;
            --spacing-sm: 0.5rem;
            --spacing-md: 1rem;
            --spacing-lg: 1.5rem;
            --spacing-xl: 2rem;
            --spacing-xxl: 3rem;

            /* Border Radius System */
            --border-radius-sm: 4px;
            --border-radius-md: 8px;
            --border-radius-lg: 12px;
            --border-radius-xl: 16px;

            /* Transition System */
            --transition-fast: 0.15s ease;
            --transition-normal: 0.3s ease;
            --transition-slow: 0.5s ease;
            --transition-bounce: 0.3s cubic-bezier(0.4, 0, 0.2, 1);
        }

        html, body {
            height: 100%;
        }

        body {
            font-family: var(--font-family);
            background: var(--body-bg);
            overflow-x: hidden;
            line-height: 1.6;
            color: var(--dark-color);
        }

        /* ===== ENHANCED PAGE STRUCTURE ===== */
        .page-container {
            background: var(--body-bg);
            min-height: calc(100vh - 100px);
        }

        .enhanced-header {
            background: var(--header-bg);
            color: white;
            padding: var(--spacing-xl) 0;
            margin-bottom: var(--spacing-xl);
            position: relative;
            overflow: hidden;
        }

        .enhanced-header::before {
            content: '';
            position: absolute;
            top: 0;
            left: 0;
            right: 0;
            bottom: 0;
            background: url('data:image/svg+xml,<svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 100 100"><defs><pattern id="grain" width="100" height="100" patternUnits="userSpaceOnUse"><circle cx="25" cy="25" r="1" fill="rgba(255,255,255,0.1)"/><circle cx="75" cy="75" r="1" fill="rgba(255,255,255,0.1)"/><circle cx="50" cy="10" r="0.5" fill="rgba(255,255,255,0.05)"/></pattern></defs><rect width="100" height="100" fill="url(%23grain)"/></svg>');
            opacity: 0.3;
        }

        .enhanced-header-content {
            position: relative;
            z-index: 2;
            max-width: 1200px;
            margin: 0 auto;
            padding: 0 var(--spacing-xl);
        }

        .enhanced-title {
            font-size: 2.5rem;
            font-weight: 700;
            margin: 0 0 var(--spacing-sm) 0;
            display: flex;
            align-items: center;
            gap: var(--spacing-md);
            text-shadow: 0 2px 4px rgba(0,0,0,0.1);
        }

        .enhanced-title-icon {
            font-size: 2.2rem;
            opacity: 0.9;
        }

        .enhanced-subtitle {
            font-size: 1.1rem;
            opacity: 0.9;
            margin: 0;
            font-weight: 400;
            letter-spacing: 0.5px;
        }

        /* ===== ENHANCED CONTENT SECTION ===== */
        .content-section {
            padding: 0 var(--spacing-xl) var(--spacing-xl);
            max-width: 1200px;
            margin: 0 auto;
        }

        /* Layout */
        .page-wrapper {
            display: flex;
            flex-direction: column;
            min-height: 100%;
        }

        .content-wrapper {
            flex: 1;
            display: flex;
        }

        /* Sidebar */
        .sidebar {
            width: 250px;
            position: fixed;
            top: 0;
            bottom: 0;
            right: 0;
            z-index: 100;
            padding: 0;
            background: linear-gradient(180deg, var(--primary-color) 0%, #303f9f 100%);
            box-shadow: var(--card-shadow);
            transition: all 0.3s ease;
            overflow-y: auto;
        }

        /* Collapsed sidebar - only show icons */
        .sidebar-collapsed .sidebar {
            width: 70px;
        }

        .sidebar-collapsed .sidebar-brand span,
        .sidebar-collapsed .nav-sidebar .nav-link span,
        .sidebar-collapsed .low-stock-alert {
            display: none;
        }

        .sidebar-collapsed .nav-sidebar .nav-link {
            justify-content: center;
            padding: 15px 0;
        }

        .sidebar-collapsed .nav-sidebar .nav-link i {
            margin-left: 0;
        }

        /* Adjust content margin when sidebar is collapsed */
        .sidebar-collapsed .main-content {
            margin-right: 70px;
            width: calc(100% - 70px);
        }

        .sidebar-header {
            padding: 20px 15px;
            display: flex;
            align-items: center;
            justify-content: space-between;
            border-bottom: 1px solid rgba(255, 255, 255, 0.1);
        }

        .sidebar-brand {
            color: white;
            font-weight: 700;
            font-size: 1.2rem;
            text-decoration: none;
            display: flex;
            align-items: center;
        }

        .sidebar-brand i {
            font-size: 1.5rem;
            margin-left: 10px;
        }

        .sidebar-collapse .sidebar-brand span {
            display: none;
        }

        .sidebar-collapse .sidebar-icon {
            margin-left: 0;
        }

        .sidebar-toggle {
            color: white;
            background: transparent;
            border: none;
            font-size: 1.2rem;
            cursor: pointer;
        }

        .nav-sidebar {
            padding: 0;
            margin: 0 0 20px;
            list-style: none;
        }

        .nav-item {
            position: relative;
        }

        .nav-sidebar .nav-link {
            color: rgba(255, 255, 255, 0.8);
            padding: 15px 20px;
            display: flex;
            align-items: center;
            transition: all 0.3s ease;
            position: relative;
        }

        .nav-sidebar .nav-link:hover {
            color: white;
            background-color: rgba(255, 255, 255, 0.1);
        }

        .nav-sidebar .nav-link.active {
            color: white;
            background-color: rgba(255, 255, 255, 0.2);
            font-weight: 600;
        }

        .nav-sidebar .nav-link.active::before {
            content: '';
            position: absolute;
            inset: 0 0 0 auto;
            width: 4px;
            background-color: var(--secondary-color);
        }

        .nav-sidebar .nav-link i {
            font-size: 1.1rem;
            margin-left: 15px;
            width: 20px;
            text-align: center;
        }

        .sidebar-collapse .nav-sidebar .nav-link span {
            display: none;
        }

        .sidebar-collapse .nav-sidebar .nav-link {
            justify-content: center;
            padding: 15px 0;
        }

        .sidebar-collapse .nav-sidebar .nav-link i {
            margin-left: 0;
        }

        /* Low stock alert in sidebar */
        .low-stock-alert {
            background-color: rgba(255, 255, 255, 0.1);
            margin: 0 15px 15px;
            padding: 15px;
            border-radius: 5px;
            color: white;
            font-size: 0.9rem;
        }

        .sidebar-collapse .low-stock-alert {
            display: none;
        }

        .low-stock-count {
            font-size: 1.5rem;
            font-weight: 700;
            display: block;
            margin-top: 5px;
        }

        .low-stock-alert a {
            color: white;
            text-decoration: none;
            font-weight: 500;
            display: flex;
            align-items: center;
            margin-top: 10px;
            font-size: 0.85rem;
        }

        .low-stock-alert a i {
            margin-left: 5px;
        }

        .badge-alert {
            position: absolute;
            top: 10px;
            left: 15px;
            padding: 3px 6px;
            border-radius: 50%;
            background-color: var(--danger-color);
            color: white;
            font-size: 0.7rem;
            min-width: 18px;
            height: 18px;
            text-align: center;
            line-height: 12px;
        }

        /* Main content */
        .main-content {
            margin-right: 250px;
            padding: 20px 30px 30px;
            width: calc(100% - 250px);
            transition: all 0.3s ease;
        }

        .sidebar-collapse .main-content {
            margin-right: 70px;
            width: calc(100% - 70px);
        }

        .page-header {
            margin-bottom: 30px;
        }

        .page-title {
            font-weight: 600;
            margin: 0;
        }

        /* Navbar (top) */
        .navbar-top {
            background-color: white;
            box-shadow: 0 1px 4px rgba(0, 0, 0, 0.05);
            padding: 12px 30px;
            z-index: 99;
        }

        .navbar-nav .nav-link {
            color: #444;
            padding: 8px 15px;
            position: relative;
        }

        .navbar-top .user-menu {
            display: flex;
            align-items: center;
        }

        .user-menu .dropdown-toggle::after {
            display: none;
        }

        .user-menu .dropdown-menu {
            border-radius: 5px;
            box-shadow: 0 5px 20px rgba(0, 0, 0, 0.1);
            border: none;
            padding: 0;
            min-width: 220px;
            margin-top: 10px;
        }

        .user-menu .dropdown-header {
            background-color: var(--primary-color);
            color: white;
            padding: 15px;
            border-radius: 5px 5px 0 0;
        }

        .user-menu .dropdown-item {
            padding: 12px 20px;
        }

        .user-menu .dropdown-item i {
            margin-left: 10px;
            width: 20px;
            text-align: center;
        }

        /* ===== ENHANCED CARDS SYSTEM ===== */
        .card {
            border: none;
            border-radius: var(--border-radius-xl);
            box-shadow: var(--card-shadow);
            margin-bottom: var(--spacing-xl);
            transition: all var(--transition-bounce);
            background: var(--card-bg);
            overflow: hidden;
        }

        .card:hover {
            transform: translateY(-4px);
            box-shadow: var(--card-shadow-hover);
        }

        .card-header {
            padding: var(--spacing-lg) var(--spacing-xl);
            background: linear-gradient(135deg, #f8f9fa 0%, #e9ecef 100%);
            border-bottom: 1px solid rgba(0, 0, 0, 0.05);
            font-weight: 600;
            display: flex;
            align-items: center;
            gap: var(--spacing-sm);
        }

        .card-header .card-title {
            margin: 0;
            font-weight: 600;
            color: var(--dark-color);
        }

        .card-header i {
            color: var(--primary-color);
        }

        .card-body {
            padding: var(--spacing-xl);
        }

        /* ===== ENHANCED STATISTICS CARDS ===== */
        .stats-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(280px, 1fr));
            gap: var(--spacing-lg);
            margin-bottom: var(--spacing-xl);
        }

        .stats-card {
            background: var(--card-bg);
            border-radius: var(--border-radius-xl);
            padding: var(--spacing-lg);
            box-shadow: var(--card-shadow);
            border: 1px solid rgba(0, 0, 0, 0.05);
            transition: all var(--transition-bounce);
            position: relative;
            overflow: hidden;
            height: 100%;
            display: flex;
            flex-direction: column;
        }

        .stats-card::before {
            content: '';
            position: absolute;
            top: 0;
            left: 0;
            right: 0;
            height: 4px;
            background: linear-gradient(90deg, var(--card-accent-color, var(--primary-color)), var(--card-accent-secondary, var(--primary-light)));
            transition: height var(--transition-normal);
        }

        .stats-card:hover {
            transform: translateY(-8px);
            box-shadow: var(--card-shadow-hover);
        }

        .stats-card:hover::before {
            height: 6px;
        }

        .stats-card-header {
            display: flex;
            align-items: center;
            justify-content: space-between;
            margin-bottom: var(--spacing-md);
        }

        .stats-card-icon {
            width: 60px;
            height: 60px;
            border-radius: var(--border-radius-lg);
            display: flex;
            align-items: center;
            justify-content: center;
            font-size: 1.5rem;
            color: white;
            background: linear-gradient(135deg, var(--card-accent-color, var(--primary-color)), var(--card-accent-secondary, var(--primary-light)));
            box-shadow: 0 4px 12px rgba(0, 0, 0, 0.15);
        }

        .stats-card-content {
            flex: 1;
            margin-bottom: var(--spacing-md);
        }

        .stats-card-title {
            font-size: 0.9rem;
            color: #6c757d;
            margin-bottom: var(--spacing-sm);
            font-weight: 500;
            letter-spacing: 0.3px;
        }

        .stats-card-value {
            font-size: 2.2rem;
            font-weight: 700;
            color: var(--dark-color);
            margin: 0;
            line-height: 1;
        }

        .stats-card-footer {
            margin-top: auto;
            padding-top: var(--spacing-md);
            border-top: 1px solid rgba(0, 0, 0, 0.05);
        }

        .stats-card-link {
            display: flex;
            align-items: center;
            justify-content: space-between;
            color: var(--card-accent-color, var(--primary-color));
            text-decoration: none;
            font-weight: 500;
            font-size: 0.9rem;
            transition: all var(--transition-fast);
            padding: var(--spacing-sm) 0;
        }

        .stats-card-link:hover {
            color: var(--card-accent-secondary, var(--primary-light));
            transform: translateX(-2px);
        }

        .stats-card-link i {
            transition: transform var(--transition-fast);
        }

        .stats-card-link:hover i {
            transform: translateX(-3px);
        }

        /* Card Color Variants */
        .stats-card-primary {
            --card-accent-color: var(--primary-color);
            --card-accent-secondary: var(--primary-light);
        }

        .stats-card-warning {
            --card-accent-color: var(--warning-color);
            --card-accent-secondary: var(--warning-light);
        }

        .stats-card-danger {
            --card-accent-color: var(--danger-color);
            --card-accent-secondary: var(--danger-light);
        }

        .stats-card-success {
            --card-accent-color: var(--success-color);
            --card-accent-secondary: var(--success-light);
        }

        .stats-card-info {
            --card-accent-color: var(--info-color);
            --card-accent-secondary: var(--info-light);
        }

        /* ===== ENHANCED BUTTONS SYSTEM ===== */
        .btn {
            border-radius: var(--border-radius-md);
            padding: var(--spacing-sm) var(--spacing-lg);
            font-weight: 500;
            transition: all var(--transition-bounce);
            border: 2px solid transparent;
            position: relative;
            overflow: hidden;
            display: inline-flex;
            align-items: center;
            gap: var(--spacing-xs);
        }

        .btn::before {
            content: '';
            position: absolute;
            top: 0;
            left: -100%;
            width: 100%;
            height: 100%;
            background: linear-gradient(90deg, transparent, rgba(255,255,255,0.2), transparent);
            transition: left var(--transition-normal);
        }

        .btn:hover::before {
            left: 100%;
        }

        .btn-primary {
            background: linear-gradient(135deg, var(--primary-color), var(--primary-light));
            border-color: var(--primary-color);
            color: white;
        }

        .btn-primary:hover {
            background: linear-gradient(135deg, var(--primary-dark), var(--primary-color));
            border-color: var(--primary-dark);
            transform: translateY(-2px);
            box-shadow: 0 4px 12px rgba(63, 81, 181, 0.3);
            color: white;
        }

        .btn-success {
            background: linear-gradient(135deg, var(--success-color), var(--success-light));
            border-color: var(--success-color);
            color: white;
        }

        .btn-success:hover {
            background: linear-gradient(135deg, var(--success-dark), var(--success-color));
            border-color: var(--success-dark);
            transform: translateY(-2px);
            box-shadow: 0 4px 12px rgba(76, 175, 80, 0.3);
            color: white;
        }

        .btn-info {
            background: linear-gradient(135deg, var(--info-color), var(--info-light));
            border-color: var(--info-color);
            color: white;
        }

        .btn-info:hover {
            background: linear-gradient(135deg, var(--info-dark), var(--info-color));
            border-color: var(--info-dark);
            transform: translateY(-2px);
            box-shadow: 0 4px 12px rgba(0, 188, 212, 0.3);
            color: white;
        }

        .btn-warning {
            background: linear-gradient(135deg, var(--warning-color), var(--warning-light));
            border-color: var(--warning-color);
            color: white;
        }

        .btn-warning:hover {
            background: linear-gradient(135deg, var(--warning-dark), var(--warning-color));
            border-color: var(--warning-dark);
            transform: translateY(-2px);
            box-shadow: 0 4px 12px rgba(255, 152, 0, 0.3);
            color: white;
        }

        .btn-danger {
            background: linear-gradient(135deg, var(--danger-color), var(--danger-light));
            border-color: var(--danger-color);
            color: white;
        }

        .btn-danger:hover {
            background: linear-gradient(135deg, var(--danger-dark), var(--danger-color));
            border-color: var(--danger-dark);
            transform: translateY(-2px);
            box-shadow: 0 4px 12px rgba(244, 67, 54, 0.3);
            color: white;
        }

        .btn-light {
            background: linear-gradient(135deg, #f8f9fa, #e9ecef);
            border-color: #dee2e6;
            color: #495057;
        }

        .btn-light:hover {
            background: linear-gradient(135deg, #e9ecef, #dee2e6);
            border-color: #adb5bd;
            transform: translateY(-2px);
            box-shadow: 0 4px 12px rgba(0, 0, 0, 0.1);
            color: #495057;
        }

        .btn-outline-primary {
            background: transparent;
            border-color: var(--primary-color);
            color: var(--primary-color);
        }

        .btn-outline-primary:hover {
            background: var(--primary-color);
            border-color: var(--primary-color);
            color: white;
            transform: translateY(-2px);
            box-shadow: 0 4px 12px rgba(63, 81, 181, 0.3);
        }

        .btn-outline-secondary {
            background: transparent;
            border-color: #6c757d;
            color: #6c757d;
        }

        .btn-outline-secondary:hover {
            background: #6c757d;
            border-color: #6c757d;
            color: white;
            transform: translateY(-2px);
            box-shadow: 0 4px 12px rgba(108, 117, 125, 0.3);
        }

        .btn-icon {
            width: 40px;
            height: 40px;
            padding: 0;
            display: inline-flex;
            align-items: center;
            justify-content: center;
            border-radius: 50%;
        }

        .btn-icon i {
            font-size: 1rem;
        }

        .btn-group .btn {
            border-radius: 0;
        }

        .btn-group .btn:first-child {
            border-radius: var(--border-radius-md) 0 0 var(--border-radius-md);
        }

        .btn-group .btn:last-child {
            border-radius: 0 var(--border-radius-md) var(--border-radius-md) 0;
        }

        /* ===== ENHANCED TABLES SYSTEM ===== */
        .table {
            margin-bottom: 0;
            border-collapse: separate;
            border-spacing: 0;
        }

        .table th {
            font-weight: 600;
            background: linear-gradient(135deg, #f8f9fa 0%, #e9ecef 100%);
            padding: var(--spacing-lg) var(--spacing-md);
            border-top: none;
            border-bottom: 2px solid var(--primary-color);
            color: var(--dark-color);
            position: relative;
            white-space: nowrap;
        }

        .table th:first-child {
            border-radius: var(--border-radius-md) 0 0 0;
        }

        .table th:last-child {
            border-radius: 0 var(--border-radius-md) 0 0;
        }

        .table td {
            padding: var(--spacing-md);
            vertical-align: middle;
            border-bottom: 1px solid rgba(0, 0, 0, 0.05);
            transition: background-color var(--transition-fast);
        }

        .table-hover tbody tr {
            transition: all var(--transition-fast);
        }

        .table-hover tbody tr:hover {
            background-color: rgba(63, 81, 181, 0.02);
            transform: scale(1.01);
            box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
        }

        .table-responsive {
            border-radius: var(--border-radius-lg);
            box-shadow: var(--card-shadow);
            overflow: hidden;
            background: white;
        }

        .table-light {
            background: linear-gradient(135deg, #f8f9fa 0%, #e9ecef 100%);
        }

        /* Enhanced Badge System */
        .badge {
            padding: var(--spacing-xs) var(--spacing-sm);
            border-radius: var(--border-radius-sm);
            font-weight: 500;
            font-size: 0.75rem;
            letter-spacing: 0.5px;
        }

        .badge.bg-primary {
            background: linear-gradient(135deg, var(--primary-color), var(--primary-light)) !important;
        }

        .badge.bg-success {
            background: linear-gradient(135deg, var(--success-color), var(--success-light)) !important;
        }

        .badge.bg-warning {
            background: linear-gradient(135deg, var(--warning-color), var(--warning-light)) !important;
        }

        .badge.bg-danger {
            background: linear-gradient(135deg, var(--danger-color), var(--danger-light)) !important;
        }

        .badge.bg-info {
            background: linear-gradient(135deg, var(--info-color), var(--info-light)) !important;
        }

        .badge.bg-secondary {
            background: linear-gradient(135deg, #6c757d, #868e96) !important;
        }

        /* ===== ENHANCED FORMS SYSTEM ===== */
        .form-control {
            padding: var(--spacing-sm) var(--spacing-md);
            border-radius: var(--border-radius-md);
            border: 2px solid #dee2e6;
            transition: all var(--transition-normal);
            background: white;
        }

        .form-control:focus {
            border-color: var(--primary-color);
            box-shadow: 0 0 0 0.25rem rgba(63, 81, 181, 0.15);
            transform: translateY(-1px);
        }

        .form-control-lg {
            padding: var(--spacing-md) var(--spacing-lg);
            font-size: 1.1rem;
        }

        .form-label {
            margin-bottom: var(--spacing-sm);
            font-weight: 600;
            color: var(--dark-color);
            display: flex;
            align-items: center;
            gap: var(--spacing-xs);
        }

        .form-text {
            color: #6c757d;
            font-size: 0.875rem;
            margin-top: var(--spacing-xs);
            display: flex;
            align-items: center;
            gap: var(--spacing-xs);
        }

        .form-select {
            padding: var(--spacing-sm) var(--spacing-md);
            border-radius: var(--border-radius-md);
            border: 2px solid #dee2e6;
            transition: all var(--transition-normal);
        }

        .form-select:focus {
            border-color: var(--primary-color);
            box-shadow: 0 0 0 0.25rem rgba(63, 81, 181, 0.15);
        }

        .input-group {
            border-radius: var(--border-radius-md);
            overflow: hidden;
        }

        .input-group .form-control {
            border-radius: 0;
        }

        .input-group .form-control:first-child {
            border-radius: var(--border-radius-md) 0 0 var(--border-radius-md);
        }

        .input-group .form-control:last-child {
            border-radius: 0 var(--border-radius-md) var(--border-radius-md) 0;
        }

        /* Responsive adjustments */
        @media (max-width: 992px) {
            .sidebar {
                width: 70px;
            }

            .sidebar-brand span,
            .nav-sidebar .nav-link span,
            .low-stock-alert {
                display: none;
            }

            .nav-sidebar .nav-link {
                justify-content: center;
                padding: 15px 0;
            }

            .nav-sidebar .nav-link i {
                margin-left: 0;
            }

            .main-content {
                margin-right: 70px;
                width: calc(100% - 70px);
            }
        }

        @media (max-width: 768px) {
            .sidebar {
                transform: translateX(100%);
                width: 250px;
                z-index: 1050;
                right: -250px;
            }

            .sidebar.show {
                transform: translateX(0);
                right: 0;
            }

            .sidebar-brand span,
            .nav-sidebar .nav-link span,
            .low-stock-alert {
                display: block;
            }

            .nav-sidebar .nav-link {
                justify-content: flex-start;
                padding: 15px 20px;
            }

            .nav-sidebar .nav-link i {
                margin-left: 15px;
            }

            .main-content {
                margin-right: 0;
                width: 100%;
            }

            .mobile-header {
                position: fixed;
                top: 0;
                left: 0;
                right: 0;
                z-index: 99;
                display: flex;
                justify-content: space-between;
                align-items: center;
                padding: 15px;
                background-color: var(--primary-color);
                color: white;
            }

            .mobile-toggle {
                font-size: 1.2rem;
                color: white;
                background: transparent;
                border: none;
                cursor: pointer;
            }

            .page-content {
                padding-top: 60px;
            }

            /* Overlay for mobile sidebar */
            .sidebar-overlay {
                display: none;
                position: fixed;
                top: 0;
                left: 0;
                right: 0;
                bottom: 0;
                background-color: rgba(0, 0, 0, 0.5);
                z-index: 1040;
            }

            .sidebar-overlay.show {
                display: block;
            }
        }

        /* Animations */
        @keyframes fadeIn {
            from { opacity: 0; transform: translateY(10px); }
            to { opacity: 1; transform: translateY(0); }
        }

        .fade-in {
            animation: fadeIn 0.5s ease forwards;
        }
    </style>

    {% block extra_css %}{% endblock %}
</head>
<body>
    <div class="page-wrapper">
        <!-- Sidebar -->
        <aside class="sidebar">
            <div class="sidebar-header">
                <a href="{% url 'inventory:dashboard' %}" class="sidebar-brand">
                    <i class="fas fa-warehouse sidebar-icon"></i>
                    <span>نظام المخزن</span>
                </a>
                <button class="sidebar-toggle d-none d-md-block">
                    <i class="fas fa-chevron-right"></i>
                </button>
            </div>

            <ul class="nav-sidebar">
                <li class="nav-item">
                    <a href="{% url 'inventory:dashboard' %}" class="nav-link {% if request.resolver_match.url_name == 'dashboard' %}active{% endif %}">
                        <i class="fas fa-home"></i>
                        <span>الرئيسية</span>
                    </a>
                </li>
                <li class="nav-item">
                    <a href="{% url 'inventory:product_list' %}" class="nav-link {% if 'product' in request.resolver_match.url_name and 'movement' not in request.resolver_match.url_name %}active{% endif %}">
                        <i class="fas fa-box"></i>
                        <span>أصناف المخزن</span>
                        {% if low_stock_count > 0 %}
                        <span class="badge-alert">{{ low_stock_count }}</span>
                        {% endif %}
                    </a>
                </li>
                <li class="nav-item">
                    <a href="{% url 'inventory:product_movement_list' %}" class="nav-link {% if 'product_movement' in request.resolver_match.url_name %}active{% endif %}">
                        <i class="fas fa-exchange-alt"></i>
                        <span>حركات الصنف</span>
                    </a>
                </li>
                <li class="nav-item">
                    <a href="{% url 'inventory:category_list' %}" class="nav-link {% if 'category' in request.resolver_match.url_name %}active{% endif %}">
                        <i class="fas fa-tags"></i>
                        <span>التصنيفات</span>
                    </a>
                </li>
                <li class="nav-item">
                    <a href="{% url 'inventory:unit_list' %}" class="nav-link {% if 'unit' in request.resolver_match.url_name %}active{% endif %}">
                        <i class="fas fa-ruler"></i>
                        <span>وحدات القياس</span>
                    </a>
                </li>
                <li class="nav-item">
                    <a href="{% url 'inventory:supplier_list' %}" class="nav-link {% if 'supplier' in request.resolver_match.url_name %}active{% endif %}">
                        <i class="fas fa-truck"></i>
                        <span>الموردين</span>
                    </a>
                </li>
                {% has_inventory_module_permission "customers" "view" as customer_view_perm %}
                {% if user.is_superuser or user|has_inventory_perm:customer_view_perm %}
                <li class="nav-item">
                    <a href="{% url 'inventory:customer_list' %}" class="nav-link {% if 'customer' in request.resolver_match.url_name %}active{% endif %}">
                        <i class="fas fa-users"></i>
                        <span>العملاء</span>
                    </a>
                </li>
                {% endif %}
                <li class="nav-item">
                    <a href="{% url 'inventory:invoice_list' %}" class="nav-link {% if 'invoice' in request.resolver_match.url_name %}active{% endif %}">
                        <i class="fas fa-file-invoice"></i>
                        <span>الفواتير</span>
                    </a>
                </li>
                <li class="nav-item">
                    <a href="{% url 'inventory:stock_report' %}" class="nav-link {% if 'stock_report' in request.resolver_match.url_name %}active{% endif %}">
                        <i class="fas fa-chart-bar"></i>
                        <span>تقارير المخزون</span>
                    </a>
                </li>
                <li class="nav-item">
                    <a href="{% url 'inventory:settings' %}" class="nav-link {% if 'settings' in request.resolver_match.url_name %}active{% endif %}">
                        <i class="fas fa-cog"></i>
                        <span>الإعدادات</span>
                    </a>
                </li>
                <li class="nav-item">
                    <a href="{% url 'accounts:home' %}" class="nav-link">
                        <i class="fas fa-chevron-circle-right"></i>
                        <span>النظام الرئيسي</span>
                    </a>
                </li>
            </ul>

            {% if low_stock_count > 0 %}
            <div class="low-stock-alert">
                <i class="fas fa-exclamation-triangle"></i> تنبيه المخزون
                <span class="low-stock-count">{{ low_stock_count }}</span>
                <span>صنف تحت الحد الأدنى</span>

                <a href="{% url 'inventory:product_list' %}?stock_status=low">
                    <i class="fas fa-arrow-left"></i> عرض القائمة
                </a>
            </div>
            {% endif %}
        </aside>

        <!-- Mobile Header (visible only on small screens) -->
        <div class="mobile-header d-md-none">
            <div class="mobile-brand">
                <i class="fas fa-warehouse me-2"></i>
                نظام المخزن
            </div>
            <button class="mobile-toggle" id="toggleSidebar">
                <i class="fas fa-bars"></i>
            </button>
        </div>

        <!-- Main Content -->
        <div class="main-content">
            {% if messages %}
            <div class="alerts-container">
                {% for message in messages %}
                <div class="alert alert-{{ message.tags }} alert-dismissible fade show" role="alert">
                    {{ message }}
                    <button type="button" class="btn-close" data-bs-dismiss="alert" aria-label="Close"></button>
                </div>
                {% endfor %}
            </div>
            {% endif %}

            {% block content %}{% endblock %}
        </div>
    </div>

    <!-- Bootstrap Bundle with Popper -->
    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.2/dist/js/bootstrap.bundle.min.js"></script>

    <script>
        // Sidebar toggle functionality
        document.addEventListener('DOMContentLoaded', function() {
            const body = document.querySelector('body');
            const sidebar = document.querySelector('.sidebar');
            const mainContent = document.querySelector('.main-content');
            const sidebarToggle = document.querySelector('.sidebar-toggle');
            const mobileToggle = document.querySelector('#toggleSidebar');

            // Create overlay element for mobile
            const overlay = document.createElement('div');
            overlay.className = 'sidebar-overlay';
            document.body.appendChild(overlay);

            // Toggle sidebar function
            function toggleSidebar() {
                // For desktop view - collapse/expand sidebar
                body.classList.toggle('sidebar-collapsed');

                // For mobile view
                if (window.innerWidth <= 768) {
                    sidebar.classList.toggle('show');
                    overlay.classList.toggle('show');
                }

                // Save state to localStorage
                const sidebarState = (window.innerWidth <= 768 && sidebar.classList.contains('show')) ||
                                   (window.innerWidth > 768 && body.classList.contains('sidebar-collapsed'))
                                   ? 'collapsed' : 'expanded';
                localStorage.setItem('inventorySidebarState', sidebarState);
            }

            // Initialize sidebar state from localStorage
            function initSidebarState() {
                const savedState = localStorage.getItem('inventorySidebarState');

                // Default to expanded on desktop, collapsed on mobile
                const defaultState = window.innerWidth <= 768 ? 'collapsed' : 'expanded';
                const sidebarState = savedState || defaultState;

                if (sidebarState === 'collapsed') {
                    if (window.innerWidth <= 768) {
                        // Mobile view - hide sidebar
                        sidebar.classList.remove('show');
                    } else {
                        // Desktop view - collapse sidebar to icons only
                        body.classList.add('sidebar-collapsed');
                    }
                } else {
                    if (window.innerWidth <= 768) {
                        // Mobile view - show sidebar
                        sidebar.classList.add('show');
                        overlay.classList.add('show');
                    } else {
                        // Desktop view - expand sidebar
                        body.classList.remove('sidebar-collapsed');
                    }
                }
            }

            // Desktop toggle
            if (sidebarToggle) {
                sidebarToggle.addEventListener('click', toggleSidebar);
            }

            // Mobile toggle
            if (mobileToggle) {
                mobileToggle.addEventListener('click', toggleSidebar);
            }

            // Initialize sidebar state
            initSidebarState();

            // Close sidebar when clicking on overlay
            overlay.addEventListener('click', function() {
                sidebar.classList.remove('show');
                overlay.classList.remove('show');
            });

            // Close sidebar when clicking outside on mobile
            document.addEventListener('click', function(e) {
                if (window.innerWidth <= 768 &&
                    sidebar.classList.contains('show') &&
                    !sidebar.contains(e.target) &&
                    e.target !== mobileToggle) {
                    sidebar.classList.remove('show');
                    overlay.classList.remove('show');
                }
            });

            // Handle window resize
            window.addEventListener('resize', function() {
                if (window.innerWidth > 768) {
                    overlay.classList.remove('show');
                }
            });
        });
    </script>

    {% block extra_js %}{% endblock %}
</body>
</html>
